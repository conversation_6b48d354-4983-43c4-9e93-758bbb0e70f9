"""
可视化策略验证器实现
"""
import logging
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import mplfinance as mpf

from ..core.interfaces.visual_validator import (
    IVisualValidator, VisualValidationConfig, VisualValidationResult, SignalPoint
)
from ..core.interfaces.data_access import IDataAccess
from ..strategies.strategy_manager import StrategyManager


class VisualValidator(IVisualValidator):
    """可视化策略验证器实现"""

    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
        self.strategy_manager = StrategyManager(data_access)
        self.logger = logging.getLogger(__name__)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

    def validate_stock_visual(self, config: VisualValidationConfig) -> VisualValidationResult:
        """对单只股票进行可视化策略验证"""
        self.logger.info(f"开始可视化验证股票: {config.stock_code}")

        try:
            # 获取股票基本信息
            stock_info = self.data_access.get_stock_info(config.stock_code)
            if not stock_info:
                return VisualValidationResult(
                    config=config,
                    stock_name="未知",
                    signal_points=[],
                    error_message=f"无法获取股票 {config.stock_code} 的基本信息"
                )

            stock_name = stock_info.get('name', config.stock_code)

            # 查找策略信号点
            signal_points = self.find_strategy_signals(
                config.stock_code,
                config.strategy_name,
                config.start_date,
                config.end_date
            )

            # 获取股票交易数据
            stock_data = self.data_access.get_stock_data(
                config.stock_code,
                config.start_date - timedelta(days=30),  # 多获取一些数据用于计算指标
                config.end_date
            )

            if not stock_data:
                return VisualValidationResult(
                    config=config,
                    stock_name=stock_name,
                    signal_points=[],
                    error_message=f"无法获取股票 {config.stock_code} 的交易数据"
                )

            # 生成图表
            chart_path = None
            if signal_points or len(stock_data) > 0:
                chart_path = self.generate_chart(config.stock_code, stock_data, signal_points, config)

            # 生成验证总结
            summary = self._generate_validation_summary(signal_points, config)

            return VisualValidationResult(
                config=config,
                stock_name=stock_name,
                signal_points=signal_points,
                chart_path=chart_path,
                total_signals=len(signal_points),
                validation_summary=summary
            )

        except Exception as e:
            self.logger.error(f"可视化验证失败: {str(e)}")
            return VisualValidationResult(
                config=config,
                stock_name="未知",
                signal_points=[],
                error_message=str(e)
            )

    def find_strategy_signals(self,
                            stock_code: str,
                            strategy_name: str,
                            start_date: datetime,
                            end_date: datetime) -> List[SignalPoint]:
        """查找策略信号点"""
        signal_points = []

        try:
            # 创建策略实例
            strategy = self.strategy_manager.create_strategy(strategy_name)
            if not strategy:
                self.logger.error(f"无法创建策略: {strategy_name}")
                return signal_points

            # 获取股票基本信息
            stock_info = self.data_access.get_stock_info(stock_code)
            if not stock_info:
                return signal_points

            # 获取足够的历史数据用于计算指标
            data_start_date = start_date - timedelta(days=60)

            # 按日期遍历，检查每一天是否有信号
            current_date = start_date
            while current_date <= end_date:
                try:
                    # 获取到当前日期为止的交易数据
                    trading_data = self.data_access.get_stock_data(stock_code, data_start_date, current_date)

                    if len(trading_data) < 30:  # 至少需要30天数据
                        current_date += timedelta(days=1)
                        continue

                    # 使用策略的内部分析方法
                    if hasattr(strategy, '_analyze_technical_reversal'):
                        result = strategy._analyze_technical_reversal(stock_code, stock_info, trading_data, current_date)

                        if result:
                            # 获取当日交易数据 - 处理数据库字段名
                            current_day_data = None
                            for d in trading_data:
                                # 处理不同的日期字段名
                                data_date = d.get('trade_date') or d.get('date')
                                if hasattr(data_date, 'date'):
                                    data_date = data_date.date()
                                elif isinstance(data_date, str):
                                    data_date = datetime.strptime(data_date, '%Y-%m-%d').date()

                                if data_date == current_date.date():
                                    current_day_data = d
                                    break

                            if current_day_data:
                                # 处理价格字段名
                                close_price = current_day_data.get('close_price') or current_day_data.get('close', 0)

                                signal_point = SignalPoint(
                                    date=current_date,
                                    price=float(close_price),
                                    signal_type='buy',
                                    score=result.get('score', 0),
                                    indicators={
                                        'rsi': result.get('rsi', 0),
                                        'volume_ratio': result.get('volume_ratio', 0),
                                        'price_position': result.get('price_position', 0),
                                        'bollinger_position': result.get('bollinger_position', 0)
                                    },
                                    reason=result.get('reason', '策略信号')
                                )
                                signal_points.append(signal_point)
                                self.logger.info(f"发现信号: {current_date.date()}, 评分: {signal_point.score}")

                except Exception as e:
                    self.logger.debug(f"处理日期 {current_date.date()} 时出错: {str(e)}")

                current_date += timedelta(days=1)

            self.logger.info(f"共找到 {len(signal_points)} 个信号点")
            return signal_points

        except Exception as e:
            self.logger.error(f"查找策略信号失败: {str(e)}")
            return signal_points

    def generate_chart(self,
                      stock_code: str,
                      stock_data: List[Dict],
                      signal_points: List[SignalPoint],
                      config: VisualValidationConfig) -> str:
        """生成K线图表"""
        try:
            # 转换数据格式
            df = self._prepare_chart_data(stock_data)
            if df.empty:
                raise ValueError("没有有效的交易数据")

            # 过滤日期范围
            df = df[(df.index >= config.start_date) & (df.index <= config.end_date)]

            if df.empty:
                raise ValueError("指定日期范围内没有交易数据")

            # 准备信号点数据
            signal_dates = [sp.date for sp in signal_points]
            signal_prices = [sp.price for sp in signal_points]

            # 创建图表
            fig, axes = plt.subplots(3, 1, figsize=config.figure_size,
                                   gridspec_kw={'height_ratios': [3, 1, 1]})

            # 主图：K线图
            ax_main = axes[0]

            # 绘制K线
            self._plot_candlestick(ax_main, df)

            # 绘制布林带
            if config.show_indicators:
                self._plot_bollinger_bands(ax_main, df)

            # 标注信号点
            if signal_points:
                self._plot_signal_points(ax_main, signal_points)

            # 设置主图标题和标签
            stock_name = self.data_access.get_stock_info(stock_code).get('name', stock_code)
            title = config.chart_title or f"{stock_name}({stock_code}) - {config.strategy_name}策略验证"
            ax_main.set_title(title, fontsize=14, fontweight='bold')
            ax_main.set_ylabel('价格', fontsize=12)

            # 成交量图
            if config.show_volume:
                ax_volume = axes[1]
                self._plot_volume(ax_volume, df, signal_dates)
                ax_volume.set_ylabel('成交量', fontsize=12)

            # RSI指标图
            if config.show_indicators:
                ax_rsi = axes[2]
                self._plot_rsi(ax_rsi, df, signal_dates)
                ax_rsi.set_ylabel('RSI', fontsize=12)
                ax_rsi.set_xlabel('日期', fontsize=12)

            # 格式化日期轴
            for ax in axes:
                ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
                ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
                plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

            # 调整布局
            plt.tight_layout()

            # 保存图表
            if config.save_path:
                save_path = config.save_path
            else:
                # 创建默认保存路径
                os.makedirs('charts', exist_ok=True)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                save_path = f"charts/{stock_code}_{config.strategy_name}_{timestamp}.png"

            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            self.logger.info(f"图表已保存到: {save_path}")
            return save_path

        except Exception as e:
            self.logger.error(f"生成图表失败: {str(e)}")
            raise

    def _prepare_chart_data(self, stock_data: List[Dict]) -> pd.DataFrame:
        """准备图表数据"""
        if not stock_data:
            return pd.DataFrame()

        # 转换为DataFrame
        df = pd.DataFrame(stock_data)

        # 数据库字段映射到标准字段名
        column_mapping = {
            'trade_date': 'date',
            'open_price': 'open',
            'high_price': 'high',
            'low_price': 'low',
            'close_price': 'close'
        }

        # 重命名列
        df = df.rename(columns=column_mapping)

        # 确保日期列是datetime类型
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)

        # 确保数值列是float类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 删除包含NaN的行
        df = df.dropna()

        # 计算技术指标
        df = self._calculate_indicators(df)

        return df

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        if len(df) < 20:
            return df

        try:
            # 计算布林带
            df['ma20'] = df['close'].rolling(window=20).mean()
            df['std20'] = df['close'].rolling(window=20).std()
            df['upper_band'] = df['ma20'] + (df['std20'] * 2)
            df['lower_band'] = df['ma20'] - (df['std20'] * 2)

            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # 计算成交量移动平均
            df['volume_ma'] = df['volume'].rolling(window=20).mean()

        except Exception as e:
            self.logger.warning(f"计算技术指标失败: {str(e)}")

        return df

    def _plot_candlestick(self, ax, df: pd.DataFrame):
        """绘制K线图"""
        # 绘制K线
        for i, (date, row) in enumerate(df.iterrows()):
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']

            # 确定颜色
            color = 'red' if close_price >= open_price else 'green'

            # 绘制影线
            ax.plot([date, date], [low_price, high_price], color='black', linewidth=0.5)

            # 绘制实体
            height = abs(close_price - open_price)
            bottom = min(open_price, close_price)

            rect = Rectangle((mdates.date2num(date) - 0.3, bottom), 0.6, height,
                           facecolor=color, alpha=0.7, edgecolor='black', linewidth=0.5)
            ax.add_patch(rect)

    def _plot_bollinger_bands(self, ax, df: pd.DataFrame):
        """绘制布林带"""
        if 'upper_band' in df.columns and 'lower_band' in df.columns and 'ma20' in df.columns:
            ax.plot(df.index, df['upper_band'], color='blue', alpha=0.5, linewidth=1, label='布林带上轨')
            ax.plot(df.index, df['ma20'], color='orange', alpha=0.7, linewidth=1, label='中轨(MA20)')
            ax.plot(df.index, df['lower_band'], color='blue', alpha=0.5, linewidth=1, label='布林带下轨')
            ax.fill_between(df.index, df['upper_band'], df['lower_band'], alpha=0.1, color='blue')
            ax.legend(loc='upper left')

    def _plot_signal_points(self, ax, signal_points: List[SignalPoint]):
        """标注信号点"""
        for signal in signal_points:
            # 买入信号用红色向上箭头
            ax.annotate('买入', xy=(signal.date, signal.price),
                       xytext=(signal.date, signal.price * 1.05),
                       arrowprops=dict(arrowstyle='->', color='red', lw=2),
                       fontsize=10, color='red', fontweight='bold',
                       ha='center')

            # 添加评分信息
            ax.text(signal.date, signal.price * 0.95, f'评分:{signal.score:.1f}',
                   fontsize=8, color='red', ha='center')

    def _plot_volume(self, ax, df: pd.DataFrame, signal_dates: List[datetime]):
        """绘制成交量"""
        if 'volume' not in df.columns:
            return

        # 绘制成交量柱状图
        colors = ['red' if close >= open else 'green'
                 for close, open in zip(df['close'], df['open'])]
        ax.bar(df.index, df['volume'], color=colors, alpha=0.7, width=0.8)

        # 绘制成交量移动平均线
        if 'volume_ma' in df.columns:
            ax.plot(df.index, df['volume_ma'], color='blue', linewidth=1, label='成交量MA20')
            ax.legend(loc='upper left')

        # 标注信号日期的成交量
        for signal_date in signal_dates:
            if signal_date in df.index:
                volume = df.loc[signal_date, 'volume']
                ax.axvline(x=signal_date, color='red', linestyle='--', alpha=0.7)

    def _plot_rsi(self, ax, df: pd.DataFrame, signal_dates: List[datetime]):
        """绘制RSI指标"""
        if 'rsi' not in df.columns:
            return

        # 绘制RSI线
        ax.plot(df.index, df['rsi'], color='purple', linewidth=1, label='RSI(14)')

        # 绘制超买超卖线
        ax.axhline(y=70, color='red', linestyle='--', alpha=0.5, label='超买线(70)')
        ax.axhline(y=30, color='green', linestyle='--', alpha=0.5, label='超卖线(30)')
        ax.axhline(y=50, color='gray', linestyle='-', alpha=0.3)

        # 设置Y轴范围
        ax.set_ylim(0, 100)
        ax.legend(loc='upper left')

        # 标注信号日期的RSI
        for signal_date in signal_dates:
            if signal_date in df.index:
                rsi_value = df.loc[signal_date, 'rsi']
                ax.axvline(x=signal_date, color='red', linestyle='--', alpha=0.7)
                ax.plot(signal_date, rsi_value, 'ro', markersize=6)

    def _generate_validation_summary(self, signal_points: List[SignalPoint], config: VisualValidationConfig) -> str:
        """生成验证总结"""
        summary = []
        summary.append(f"策略验证总结 - {config.strategy_name}")
        summary.append("=" * 50)
        summary.append(f"股票代码: {config.stock_code}")
        summary.append(f"验证期间: {config.start_date.date()} 至 {config.end_date.date()}")
        summary.append(f"总信号数量: {len(signal_points)}")

        if signal_points:
            summary.append("\n信号详情:")
            summary.append("-" * 30)
            for i, signal in enumerate(signal_points, 1):
                summary.append(f"{i}. {signal.date.date()}")
                summary.append(f"   价格: {signal.price:.2f}")
                summary.append(f"   评分: {signal.score:.1f}")
                summary.append(f"   RSI: {signal.indicators.get('rsi', 0):.1f}")
                summary.append(f"   量比: {signal.indicators.get('volume_ratio', 0):.2f}")
                summary.append(f"   原因: {signal.reason}")
                summary.append("")
        else:
            summary.append("\n在指定期间内未发现符合策略条件的信号点。")

        return "\n".join(summary)